"use client";
import { <PERSON>, <PERSON>rop<PERSON> } from "next/font/google"
import { ReactNode, useState } from "react";

type Params = {
    params: { adddetails: string }
}

type ProductDetails = {
    title?: string | null,
    items?: string | null,
    regular?: number| null,
    discount?:number| null,
    discrate?:number| null,
    brandtitle?:string| null,
    brandimg?:string | null,
    colorCode?: string | null,
    dressSize?: string | null
}

const manrope = Manrope({
    subsets:["latin"]
})

const anton = Anton({
    subsets: ["latin"],
    weight: "400"
})
export default function AddDetails({ params }: Params) {
    const [productDetails,setDetails] = useState<ProductDetails>();
    const [colorCode,setColorCode] = useState<string[] | null | undefined>([]);
    const [dressSize,setSize] = useState<string[] | null | undefined>([]);

    const handlerInputChange=(event:React.ChangeEvent<HTMLInputElement>)=>{
        const {name,value} = event.target;

        setDetails({...productDetails,[name]:value})
    }

    const reuseableInput=(placeholder:string | null,value:string | number | null,name:string)=>{
        return <>
        <input type="text" value={value ?? ""} name={name} className={`${manrope.className} h-full w-full px-3 placeholder:capitalize placeholder:text-black/40 placeholder:font-bold text-black/80 font-medium focus:outline-black/10 bg-white rounded-lg shadow-xs shadow-black/10`} placeholder={`${placeholder}`}
        onChange={(event)=>{handlerInputChange(event)}}
        />
        </>
    }

    const getValue=(condition:string)=>{
        const copy = condition == "colorCode" ? colorCode : dressSize;
        const update = condition == "colorCode" ? [...copy,productDetails?.colorCode] :[...copy,productDetails?.dressSize];

        if(condition == "colorCode"){
            setColorCode(update)
        }else{
            setSize(update)
        }
    }
    return (
        <>
        <section className="mt-5 px-5">
            <div className="grid grid-cols-2 gap-x-5">
                <div>
                    <div className="flex flex-row gap-x-5">
                        <div className="h-[120px] w-full shadow-xs shadow-black/20 rounded-lg bg-white">

                        </div>
                        <div className="h-[120px] w-full shadow-xs shadow-black/20 rounded-lg bg-white">

                        </div>
                        <div className="h-[120px] w-full shadow-xs shadow-black/20 rounded-lg bg-white">

                        </div>
                        <div className="h-[120px] w-full shadow-xs shadow-black/20 rounded-lg bg-white">

                        </div>
                    </div>

                    <div className="mt-10">
                        <h2 className={`${anton.className} uppercase text-4xl font-bold tracking-widest`} style={{WebkitTextStroke:'2px black',WebkitTextFillColor:"transparent"}}>
                            details
                        </h2>

                        <div className="h-[250px] w-full shadow-xs shadow-black/20 bg-white rounded-lg mt-5">

                        </div>
                    </div>
                </div>

                <div>
                    <div className="flex flex-row gap-x-5">
                        <div className="h-10 w-full">
                            {reuseableInput("title",productDetails?.title || "","title")}
                        </div>
                        <div className="h-10 w-full">
                            {reuseableInput("items",productDetails?.items ?? "","items")}
                        </div>
                    </div>

                    <div className="mt-5">
                        <div className="flex flex-row gap-x-5 items-center">
                            <div>
                                <h3 className={`${anton.className} text-2xl font-medium tracking-wider`}>
                                    Color
                                </h3>
                            </div>
                            <div className="h-10 w-full">
                            {reuseableInput("color code",productDetails?.colorCode ?? "","colorCode")}
                            </div>

                            <div className="w-full flex flex-row justify-end">
                                <button className={`${manrope.className} bg-[#2ecc71] px-3 py-2 rounded-xl text-white font-bold shadow-[2px_2px_2px_#16a085] transition-all duration-200 ease-linear hover:cursor-pointer active:scale-95 hover:shadow-[1px_1px_1px_#16a085] hover:bg-[#27ae60]`} onClick={()=>{getValue("colorCode")}}>
                                    add
                                </button>
                            </div>
                        </div>

                        <div className="mt-[10px] w-full py-5 bg-white rounded-lg shadow-xs shadow-black/20">

                        </div>
                    </div>

                    <div className="mt-5">
                        <div className="flex flex-row gap-x-5 items-center">
                            <div>
                                <h3 className={`${anton.className} text-2xl font-medium tracking-wider`}>
                                    size
                                </h3>
                            </div>
                            <div className="h-10 w-full">
                            {reuseableInput("dress size",productDetails?.dre ?? "", "size")}
                            </div>

                            <div className="w-full flex flex-row justify-end">
                                <button className={`${manrope.className} bg-[#2ecc71] px-3 py-2 rounded-xl text-white font-bold shadow-[2px_2px_2px_#16a085] transition-all duration-200 ease-linear hover:cursor-pointer active:scale-95 hover:shadow-[1px_1px_1px_#16a085] hover:bg-[#27ae60]`} onClick={()=>{getValue("size")}}>
                                    add
                                </button>
                            </div>
                        </div>

                        <div className="mt-[10px] w-full py-5 bg-white rounded-lg shadow-xs shadow-black/20">

                        </div>
                    </div>

                    <div className="flex flex-row gap-x-5 mt-5">
                        <div className="h-10 w-full">
                            {reuseableInput("regular price",productDetails?.regular ?? "","regular")}
                        </div>

                        <div className="h-10 w-full">
                            {reuseableInput("discount price",productDetails?.discount ?? "","discount")}
                        </div>

                        <div className="h-10 w-full">
                            {reuseableInput("discount rate",productDetails?.discrate ?? "","discrate")}
                        </div>
                    </div>

                    <div className="mt-5">
                        <div className="flex flex-row gap-x-5 items-center">
                            <div className="w-full">
                                <h3 className={`${anton.className} text-2xl font-medium tracking-wider`}>
                                    Brand title
                                </h3>
                            </div>
                            <div className="h-10 w-full">
                            {reuseableInput("brand title",productDetails?.brandtitle ?? "","brandtitle")}
                            </div>

                            <div className="w-[5%]">
                                
                            </div>
                        </div>

                        <div className="mt-[10px] w-full h-20 bg-white rounded-xl shadow-xs shadow-black/20">

                        </div>
                    </div>
                </div>
            </div>

            <div className="mt-10">
                <button className={`${anton.className} bg-[#3498db] px-4 py-2 text-white tracking-widest text-xl rounded-xl shadow-[4px_3px_2px_#2980b9] transition-all duration-75 ease-linear hover:shadow-[3px_3px_4px_#3498db] hover:bg-[#2980b9] hover:cursor-pointer`}>
                    add new product
                </button>
            </div>
        </section>
        </>
    )
}